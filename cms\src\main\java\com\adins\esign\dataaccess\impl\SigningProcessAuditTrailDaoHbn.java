package com.adins.esign.dataaccess.impl;

import java.math.BigInteger;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.businesslogic.BaseDaoHbn;
import com.adins.am.model.AmMsuser;
import com.adins.esign.constants.GlobalKey;
import com.adins.esign.constants.GlobalVal;
import com.adins.esign.dataaccess.api.SigningProcessAuditTrailDao;
import com.adins.esign.model.MsTenant;
import com.adins.esign.model.MsVendorRegisteredUser;
import com.adins.esign.model.TrBalanceMutation;
import com.adins.esign.model.TrSigningProcessAuditTrail;
import com.adins.esign.model.TrSigningProcessAuditTrailDetail;
import com.adins.esign.util.MssTool;
import com.adins.esign.webservices.model.DownloadAuditTrailExcelRequest;
import com.adins.esign.webservices.model.InquiryAuditTrailSignProcessRequest;

@Transactional
@Component
public class SigningProcessAuditTrailDaoHbn extends BaseDaoHbn implements SigningProcessAuditTrailDao {

	@Override
	public void insertSigningProcessAuditTrail(TrSigningProcessAuditTrail auditTrail) {
		auditTrail.setUsrCrt(MssTool.maskData(auditTrail.getUsrCrt()));
		managerDAO.insert(auditTrail);
	}

	@Transactional(propagation = Propagation.REQUIRES_NEW)
	@Override
	public void insertSigningProcessAuditTrailNewTr(TrSigningProcessAuditTrail auditTrail) {
		auditTrail.setUsrCrt(MssTool.maskData(auditTrail.getUsrCrt()));
		managerDAO.insert(auditTrail);
	}

	@Override
	public void updateSigningProcessAuditTrail(TrSigningProcessAuditTrail auditTrail) {
		auditTrail.setUsrUpd(MssTool.maskData(auditTrail.getUsrUpd()));
		this.managerDAO.update(auditTrail);
	}

	@Override
	public void insertSigningProcessAuditTrailDetail(TrSigningProcessAuditTrailDetail auditTrailDetail) {
		auditTrailDetail.setUsrCrt(MssTool.maskData(auditTrailDetail.getUsrCrt()));
		managerDAO.insert(auditTrailDetail);
	}

	@Transactional(propagation = Propagation.REQUIRES_NEW)
	@Override
	public void insertSigningProcessAuditTrailDetailNewTr(TrSigningProcessAuditTrailDetail auditTrailDetail) {
		auditTrailDetail.setUsrCrt(MssTool.maskData(auditTrailDetail.getUsrCrt()));
		managerDAO.insert(auditTrailDetail);
	}

	@Override
	public TrSigningProcessAuditTrail getSigningProcessAuditTrailByIdAndEmail(long id,String email) {
		return this.managerDAO.selectOne(
				"from TrSigningProcessAuditTrail tspat "
				+ " where tspat.idSigningProcessAuditTrail = :idSigningProcessAuditTrail "
						+ " and tspat.email = :email ",
				new Object[][] {{TrSigningProcessAuditTrail.ID_TR_SIGNING_PROCESS_AUDIT_TRAIL_HBM, id},{"email", email}});
	}

	private StringBuilder buildConditionalParam(Map<String, Object> paramsQuery, InquiryAuditTrailSignProcessRequest request) {
		StringBuilder conditionalParam = new StringBuilder();

		if (StringUtils.isNotBlank(request.getProcessType())) {
			conditionalParam.append(" and mlpt.code = :codeProcessType ");
			paramsQuery.put("codeProcessType", request.getProcessType());
		}
		if (StringUtils.isNotBlank(request.getTenantCode())) {
			conditionalParam.append(" and mt.tenant_code = :tenantCode ");
			paramsQuery.put("tenantCode", request.getTenantCode());
		}
		if (StringUtils.isNotBlank(request.getRefNumber())) {
			conditionalParam.append(" and tdh.ref_number = :refNumber ");
			paramsQuery.put("refNumber", request.getRefNumber());
		}



		if(StringUtils.isNotBlank(request.getInquiryStartDate())) {

			Date start = MssTool.formatStringToDate(request.getInquiryStartDate(), GlobalVal.DATE_FORMAT);
			conditionalParam.append(" and DATE(tspat.dtm_crt) >= :inquiryStartDate ");
			paramsQuery.put("inquiryStartDate", start);

		}

		if (StringUtils.isNotBlank(request.getInquiryEndDate())) {

			Date end = MssTool.formatStringToDate(request.getInquiryEndDate(), GlobalVal.DATE_FORMAT);
			conditionalParam.append(" and DATE(tspat.dtm_crt) <= :inquiryEndDate ");
			paramsQuery.put("inquiryEndDate", end);

		}

		return conditionalParam;
	}

	private StringBuilder buildDownloadExcelConditionalParam(Map<String, Object> paramsQuery, DownloadAuditTrailExcelRequest request) {
		StringBuilder conditionalParam = new StringBuilder();

		if (StringUtils.isNotBlank(request.getProcessType())) {
			conditionalParam.append(" and mlpt.code = :codeProcessType ");
			paramsQuery.put("codeProcessType", request.getProcessType());
		}
		if (StringUtils.isNotBlank(request.getTenantCode())) {
			conditionalParam.append(" and mt.tenant_code = :tenantCode ");
			paramsQuery.put("tenantCode", request.getTenantCode());
		}
		if (StringUtils.isNotBlank(request.getRefNumber())) {
			conditionalParam.append(" and tdh.ref_number = :refNumber ");
			paramsQuery.put("refNumber", request.getRefNumber());
		}



		if(StringUtils.isNotBlank(request.getInquiryStartDate())) {

			Date start = MssTool.formatStringToDate(request.getInquiryStartDate(), GlobalVal.DATE_FORMAT);
			conditionalParam.append(" and tspat.dtm_crt >= :inquiryStartDate ");
			paramsQuery.put("inquiryStartDate", start);

		}

		if (StringUtils.isNotBlank(request.getInquiryEndDate())) {

			Date end = MssTool.formatStringToDate(request.getInquiryEndDate(), GlobalVal.DATE_FORMAT);
			conditionalParam.append(" and tspat.dtm_crt <= :inquiryEndDate ");
			paramsQuery.put("inquiryEndDate", end);

		}

		return conditionalParam;
	}

	@Override
	public List<Map<String, Object>> getListInquiryAuditTrail(InquiryAuditTrailSignProcessRequest request, MsVendorRegisteredUser vendorUser, int start, int end) {
		StringBuilder query = new StringBuilder();
		Map<String, Object> params = new HashMap<>();
		StringBuilder conditionalParam = this.buildConditionalParam(params, request);


		params.put("start", start);
		params.put("end", end);
		params.put(AmMsuser.ID_MS_USER_HBM, vendorUser.getAmMsuser().getIdMsUser());

		query
		.append(" select * from ( ")
		.append(" select row_number() over(order by tspat.dtm_crt) as row  ")
		.append(" , coalesce(am.login_id) LoginId,mt.tenant_code,mv.vendor_Code,tspat.dtm_Crt,mlpt.description as \"ProcessType\",phone_no_bytea,id_no_bytea ")
		.append(" , case when (tspat.result_status = '1') then  'Success' ")
		.append(" else  'Failed' ")
		.append(" end as result_status ,coalesce(notification_media,'') NotificationMedia,coalesce(notification_vendor,'') NotificationVendor")
		.append(" ,coalesce(mlsp.description,'') as  \"SendingPoint\" ,coalesce(tspat.otp_code,'') OtpCode,coalesce(tspat.notes,'') Notes, tspat.id_signing_process_audit_trail")
		.append(" , case ")
		.append("   when docCount.docDetailCount > 0 then '1'")
		.append("   else '0'")
		.append("   end as documentDetailStatus ")
		.append(" from tr_signing_process_audit_trail tspat  ")
		.append(" left join lateral ( select invitation_code,id_ms_user from tr_invitation_link til ")
		.append("                     where tspat.id_invitation_link = til.id_invitation_link ")
		.append("                     union")
		.append("                     select invitation_code,id_ms_user from tr_invitation_link_history tilh")
		.append("                     where tspat.id_invitation_link_history = tilh.id_invitation_link_history")
		.append("                     )ts on true")
		.append(" left join lateral ( select count (*) as docDetailCount from tr_signing_process_audit_trail_detail tspatd ")
		.append("                       where tspatd.id_signing_process_audit_trail = tspat.id_signing_process_audit_trail ")
		.append("                    )docCount on true")
		.append(" join am_msuser am on am.id_ms_user = coalesce(tspat.id_ms_user,ts.id_ms_user) ")
		.append(" join ms_tenant mt on mt.id_ms_tenant = tspat.id_ms_tenant ")
		.append(" join ms_vendor mv on mv.id_ms_vendor = tspat.psre_vendor_id ")
		.append(" join ms_lov mlpt on mlpt.id_lov = tspat.lov_process_type  ")
		.append(" join am_user_personal_data aupd on aupd.id_ms_user = am.id_ms_user ")
		.append(" left join ms_lov mlsp on mlsp.id_lov = tspat.lov_sending_point ");
		if (StringUtils.isNotBlank(request.getRefNumber())) {
			query
			.append(" join lateral ( ")
			.append(" select tdh.ref_number from tr_signing_process_audit_trail_detail tspatd  ")
			.append(" join tr_document_d tdd on tspatd.id_document_d = tdd.id_document_d  ")
			.append(" join tr_document_h tdh on tdh.id_document_h = tdd.id_document_h   ")
			.append(" where  tspatd.id_signing_process_audit_trail = tspat.id_signing_process_audit_trail ")
			.append(" limit 1 ")
			.append(" ) tdh on true ");
		}
		query
		.append(" where (tspat.id_ms_user = :idMsUser OR (tspat.id_ms_user IS NULL AND ts.id_ms_user = :idMsUser)) ")
		.append(conditionalParam)
		.append(" order by tspat.dtm_crt  " )
		.append(") as b ")
		.append(" where  b.row between :start and :end ")
		.append(" order by b.row ")
		;

		return this.managerDAO.selectAllNativeString(query.toString(), params);
	}

	@Override
	public BigInteger getCountListInquiryAuditTrail(InquiryAuditTrailSignProcessRequest request, MsVendorRegisteredUser vendorUser) {
		StringBuilder query = new StringBuilder();
		Map<String, Object> params = new HashMap<>();
		StringBuilder conditionalParam = this.buildConditionalParam(params, request);



		params.put(AmMsuser.ID_MS_USER_HBM, vendorUser.getAmMsuser().getIdMsUser());

		query
		.append(" select count(1) from ( ")
		.append(" select tspat.id_ms_user,tspat.otp_code,tspat.notes")
		.append(" from tr_signing_process_audit_trail tspat  ")
		.append(" left join lateral ( select invitation_code,til.id_ms_user from tr_invitation_link til ")
		.append("                     where tspat.id_invitation_link = til.id_invitation_link ")
		.append("                     union")
		.append("                     select invitation_code,tilh.id_ms_user from tr_invitation_link_history tilh")
		.append("                     where tspat.id_invitation_link_history = tilh.id_invitation_link_history")
		.append("                     )ts on true")
		.append(" left join lateral ( select count (*) as docDetailCount from tr_signing_process_audit_trail_detail tspatd ")
		.append("                       where tspatd.id_signing_process_audit_trail = tspat.id_signing_process_audit_trail ")
		.append("                    )docCount on true")
		.append(" join am_msuser am on am.id_ms_user = coalesce(tspat.id_ms_user,ts.id_ms_user) ")
		.append(" join ms_tenant mt on mt.id_ms_tenant = tspat.id_ms_tenant ")
		.append(" join ms_vendor mv on mv.id_ms_vendor = tspat.psre_vendor_id ")
		.append(" join ms_lov mlpt on mlpt.id_lov = tspat.lov_process_type  ")
		.append(" join am_user_personal_data aupd on aupd.id_ms_user = am.id_ms_user ")
		.append(" left join ms_lov mlsp on mlsp.id_lov = tspat.lov_sending_point ");
		if (StringUtils.isNotBlank(request.getRefNumber())) {
			query
			.append(" join lateral ( ")
			.append(" select tdh.ref_number from tr_signing_process_audit_trail_detail tspatd  ")
			.append(" join tr_document_d tdd on tspatd.id_document_d = tdd.id_document_d  ")
			.append(" join tr_document_h tdh on tdh.id_document_h = tdd.id_document_h   ")
			.append(" where  tspatd.id_signing_process_audit_trail = tspat.id_signing_process_audit_trail ")
			.append(" limit 1 ")
			.append(" ) tdh on true ");
		}
		query
		.append(" where (tspat.id_ms_user = :idMsUser OR (tspat.id_ms_user IS NULL AND ts.id_ms_user = :idMsUser)) ")
		.append(conditionalParam)
		.append(" order by tspat.id_signing_process_audit_trail " )
		.append(") as b ");

		return (BigInteger) this.managerDAO.selectOneNativeString(query.toString(), params);
	}


	@Override
	public List<Map<String, Object>> getInquiryAuditTrailDetail(long idSigningProcessAuditTrail) {
		StringBuilder query = new StringBuilder();
		Map<String, Object> params = new HashMap<>();

		params.put(TrSigningProcessAuditTrail.ID_TR_SIGNING_PROCESS_AUDIT_TRAIL_HBM, idSigningProcessAuditTrail);

		query
		.append(" select tdh.ref_number,tdd.document_id from tr_signing_process_audit_trail tspat ")
		.append(" join tr_signing_process_audit_trail_detail tspatd on tspat.id_signing_process_audit_trail = tspatd.id_signing_process_audit_trail  ")
		.append(" join tr_document_d tdd on tdd.id_document_d = tspatd.id_document_d ")
		.append(" join tr_document_h tdh on tdh.id_document_h = tdd.id_document_h ")
		.append(" where tspat.id_signing_process_audit_trail = :idSigningProcessAuditTrail ");

		return this.managerDAO.selectAllNativeString(query.toString(), params);
	}

	@Override
	public List<Map<String, Object>> downloadListInquiryAuditTrail(DownloadAuditTrailExcelRequest request,
			AmMsuser user) {
				StringBuilder query = new StringBuilder();
				Map<String, Object> params = new HashMap<>();
				StringBuilder conditionalParam = this.buildDownloadExcelConditionalParam(params, request);

				params.put(AmMsuser.ID_MS_USER_HBM, user.getIdMsUser());

				query
				.append(" select row_number() over(order by tspat.dtm_crt) as row  ")
				.append(" , coalesce(am.login_id) LoginId,mt.tenant_code,mv.vendor_Code,tspat.dtm_Crt,mlpt.description as \"ProcessType\",phone_no_bytea,id_no_bytea ")
				.append(" , case when (tspat.result_status = '1') then  'Success' ")
				.append(" else  'Failed' ")
				.append(" end as result_status ,coalesce(notification_media,'') NotificationMedia,coalesce(notification_vendor,'') NotificationVendor")
				.append(" ,coalesce(mlsp.description,'') as  \"SendingPoint\" ,coalesce(tspat.otp_code,'') OtpCode,coalesce(tspat.notes,'') Notes, tspat.id_signing_process_audit_trail")
				.append(" from tr_signing_process_audit_trail tspat  ")
				.append(" left join lateral ( select invitation_code,id_ms_user from tr_invitation_link til ")
				.append("                     where tspat.id_invitation_link = til.id_invitation_link ")
				.append("                     union")
				.append("                     select invitation_code,id_ms_user from tr_invitation_link_history tilh")
				.append("                     where tspat.id_invitation_link_history = tilh.id_invitation_link_history")
				.append("                     )ts on true")
				.append(" join am_msuser am on am.id_ms_user = coalesce(tspat.id_ms_user,ts.id_ms_user) ")
				.append(" join ms_tenant mt on mt.id_ms_tenant = tspat.id_ms_tenant ")
				.append(" join ms_vendor mv on mv.id_ms_vendor = tspat.psre_vendor_id ")
				.append(" join ms_lov mlpt on mlpt.id_lov = tspat.lov_process_type  ")
				.append(" join am_user_personal_data aupd on aupd.id_ms_user = am.id_ms_user ")
				.append(" left join ms_lov mlsp on mlsp.id_lov = tspat.lov_sending_point ");
				if (StringUtils.isNotBlank(request.getRefNumber())) {
					query
					.append(" join lateral ( ")
					.append(" select tdh.ref_number from tr_signing_process_audit_trail_detail tspatd  ")
					.append(" join tr_document_d tdd on tspatd.id_document_d = tdd.id_document_d  ")
					.append(" join tr_document_h tdh on tdh.id_document_h = tdd.id_document_h   ")
					.append(" where  tspatd.id_signing_process_audit_trail = tspat.id_signing_process_audit_trail ")
					.append(" limit 1 ")
					.append(" ) tdh on true ");
				}
				query
				.append(" where (tspat.id_ms_user = :idMsUser OR (tspat.id_ms_user IS NULL AND ts.id_ms_user = :idMsUser)) ")
				.append(conditionalParam)
				.append(" order by tspat.dtm_crt  " );

				return this.managerDAO.selectAllNativeString(query.toString(), params);
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<TrSigningProcessAuditTrail> getListAuditTrailByIdInvitationLink(long idInvitationLink) {
		Object[][] queryParams = {
				{"idInvitationLink", idInvitationLink}
		};

		return (List<TrSigningProcessAuditTrail>) this.managerDAO.list(
				"from TrSigningProcessAuditTrail at "
				+ "join fetch at.trInvitationLink i "
				+ "where i.idInvitationLink = :idInvitationLink ", queryParams)
				.get(GlobalKey.MAP_RESULT_LIST);
	}
}


